<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capybara Coloring App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: bold;
            color: #555;
        }

        /* Color Palette */
        .color-palette {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 5px;
            max-width: 300px;
        }

        .color-option1 {
            width: 40px;
            height: 40px;
            border: 3px solid transparent;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option1:hover {
            transform: scale(1.1);
            border-color: #333;
        }

        .color-option1.active {
            border-color: #000;
            transform: scale(1.2);
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }

        /* Tools */
        .tools {
            display: flex;
            gap: 10px;
        }

        .color-option2 {
            padding: 10px 15px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .color-option2:hover {
            background: #f0f0f0;
            border-color: #999;
        }

        .color-option2.toolActive {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        /* Brush Sizes */
        .sizes {
            display: flex;
            gap: 10px;
        }

        .itemSize {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            transition: all 0.3s ease;
        }

        .itemSize:hover {
            border-color: #999;
            background: #f0f0f0;
        }

        .itemSize.sizeActive {
            border-color: #007bff;
            background: #007bff;
        }

        .itemSize::before {
            content: '';
            background: #333;
            border-radius: 50%;
        }

        .itemSize[data-size="least"]::before { width: 4px; height: 4px; }
        .itemSize[data-size="small"]::before { width: 8px; height: 8px; }
        .itemSize[data-size="medium"]::before { width: 12px; height: 12px; }
        .itemSize[data-size="large"]::before { width: 16px; height: 16px; }
        .itemSize[data-size="huge"]::before { width: 20px; height: 20px; }

        .itemSize.sizeActive::before {
            background: white;
        }

        /* Canvas Container */
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }

        #canvasDiv {
            display: inline-block;
            border: 3px solid #333;
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        canvas {
            display: block;
            border-radius: 7px;
        }

        /* Download Button */
        .download-section {
            text-align: center;
            margin-top: 20px;
        }

        #download {
            padding: 12px 24px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        #download:hover {
            background: #218838;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .color-palette {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦫 Ứng Dụng Tô Màu Capybara</h1>
        
        <div class="controls">
            <!-- Color Palette -->
            <div class="control-group">
                <label>Chọn Màu:</label>
                <div class="color-palette" id="colorPalette">
                    <!-- Colors will be generated by JavaScript -->
                </div>
            </div>

            <!-- Tools -->
            <div class="control-group">
                <label>Công Cụ:</label>
                <div class="tools">
                    <div class="color-option2 toolActive" id="marker">Bút Vẽ</div>
                    <div class="color-option2" id="eraser">Tẩy</div>
                    <div class="color-option2" id="clear">Xóa Hết</div>
                    <div class="color-option2" id="undo">Hoàn Tác</div>
                </div>
            </div>

            <!-- Brush Sizes -->
            <div class="control-group">
                <label>Kích Thước:</label>
                <div class="sizes">
                    <div class="itemSize" data-size="least" id="least"></div>
                    <div class="itemSize sizeActive" data-size="small" id="small"></div>
                    <div class="itemSize" data-size="medium" id="medium"></div>
                    <div class="itemSize" data-size="large" id="large"></div>
                    <div class="itemSize" data-size="huge" id="huge"></div>
                </div>
            </div>
        </div>

        <!-- Canvas -->
        <div class="canvas-container">
            <div id="canvasDiv">
                <canvas id="canvas"></canvas>
            </div>
        </div>

        <!-- Download -->
        <div class="download-section">
            <a href="#" id="download" download="capybara-coloring.png">💾 Tải Xuống Tranh</a>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        var drawingApp = (function () {
            "use strict";
            
            var canvas,
                context,
                // 36 màu như code mẫu
                colors = [
                    "#F90B01", "#B51A01", "#A84D02", "#F98001", "#F9BC7E", "#F9F97E",
                    "#E4E20B", "#A3BC08", "#90FC84", "#03CB01", "#5B9908", "#4E4C01",
                    "#013D01", "#8BEDDC", "#43AEEB", "#8F9EF9", "#0D30F9", "#13229E",
                    "#01083F", "#480143", "#780F8F", "#6F06C1", "#C58EFB", "#F9B7CE",
                    "#F9656F", "#A10151", "#5B0101", "#6E3511", "#674733", "#BCA383",
                    "#FFFF00", "#858585", "#3F3F3F", "#000000", "#DF0029", "#00B2BF"
                ],
                outlineImage = new Image(),
                clickX = [],
                clickY = [],
                clickColor = [],
                clickTool = [],
                clickSize = [],
                clickDrag = [],
                paint = false,
                curColor = colors[9], // Default green
                curTool = "marker",
                curSize = "small",
                canvasWidth,
                canvasHeight,
                drawingAreaX = 0,
                drawingAreaY = 0,
                drawingAreaWidth,
                drawingAreaHeight,
                totalLoadResources = 1,
                curLoadResNum = 0,
                downloadImg = 'capybara-coloring';

            var url = 'https://tomau.vn/wp-content/uploads/Tranh-To-Mau-Capybara-va-Cay-Dua.jpg';

            // Clear canvas
            var clearCanvas = function () {
                context.clearRect(0, 0, canvasWidth, canvasHeight);
            };

            // Set save function
            var setSave = function() {
                var a = canvas.toDataURL(),
                    save = document.getElementById('download');
                save.href = a;
                save.download = downloadImg + ".png";
            };

            // Redraw canvas
            var redraw = function () {
                var locX, locY, radius, i;

                // Make sure required resources are loaded before redrawing
                if (curLoadResNum < totalLoadResources) {
                    return;
                }

                clearCanvas();

                // Handle special tools
                if (curTool === "undo") {
                    // Remove last 5 points (one stroke)
                    for(var i = 1; i <= 5 && clickX.length > 0; i += 1) {
                        clickX.pop();
                        clickY.pop();
                        clickDrag.pop();
                        clickTool.pop();
                        clickColor.pop();
                        clickSize.pop();
                    }
                    curTool = "marker"; // Reset to marker after undo
                }

                // Keep the drawing in the drawing area
                context.save();
                context.beginPath();
                context.rect(drawingAreaX, drawingAreaY, drawingAreaWidth, drawingAreaHeight);
                context.clip();

                // For each point drawn
                for (i = 0; i < clickX.length; i += 1) {
                    // Set the drawing radius based on size
                    switch (clickSize[i]) {
                        case "least": radius = 2; break;
                        case "small": radius = 5; break;
                        case "medium": radius = 10; break;
                        case "large": radius = 15; break;
                        case "huge": radius = 25; break;
                        default: radius = 5; break;
                    }

                    // Set the drawing path
                    context.beginPath();
                    // If dragging then draw a line between the two points
                    if (clickDrag[i] && i) {
                        context.moveTo(clickX[i - 1], clickY[i - 1]);
                    } else {
                        // The x position is moved over one pixel so a circle even if not dragging
                        context.moveTo(clickX[i] - 1, clickY[i]);
                    }
                    context.lineTo(clickX[i], clickY[i]);

                    // Set the drawing color
                    if (clickTool[i] === "eraser") {
                        context.strokeStyle = 'white';
                    } else {
                        context.strokeStyle = clickColor[i];
                    }
                    context.lineCap = "round";
                    context.lineJoin = "round";
                    context.lineWidth = radius;
                    context.stroke();
                }
                context.closePath();
                context.restore();

                // Draw the outline image with multiply mode (key technique from sample code)
                context.globalCompositeOperation = "multiply";
                context.drawImage(outlineImage, drawingAreaX, drawingAreaY, drawingAreaWidth, drawingAreaHeight);
                context.globalCompositeOperation = "source-over";
            };

            // Add a point to the drawing array
            var addClick = function (x, y, dragging) {
                clickX.push(x);
                clickY.push(y);
                clickTool.push(curTool);
                clickColor.push(curColor);
                clickSize.push(curSize);
                clickDrag.push(dragging);
            };

            // Create user events
            var createUserEvents = function () {
                var press = function (e) {
                    var mouseX = (e.changedTouches ? e.changedTouches[0].pageX : e.pageX) - this.offsetLeft,
                        mouseY = (e.changedTouches ? e.changedTouches[0].pageY : e.pageY) - this.offsetTop;

                    paint = true;
                    addClick(mouseX, mouseY, false);
                    redraw();
                };

                var drag = function (e) {
                    var mouseX = (e.changedTouches ? e.changedTouches[0].pageX : e.pageX) - this.offsetLeft,
                        mouseY = (e.changedTouches ? e.changedTouches[0].pageY : e.pageY) - this.offsetTop;

                    if (paint) {
                        addClick(mouseX, mouseY, true);
                        redraw();
                    }
                    // Prevent the whole page from dragging if on mobile
                    e.preventDefault();
                };

                var release = function () {
                    paint = false;
                    redraw();
                };

                var cancel = function () {
                    paint = false;
                };

                // Add mouse event listeners to canvas element
                canvas.addEventListener("mousedown", press, false);
                canvas.addEventListener("mousemove", drag, false);
                canvas.addEventListener("mouseup", release);
                canvas.addEventListener("mouseout", cancel, false);

                // Add touch event listeners to canvas element
                canvas.addEventListener("touchstart", press, false);
                canvas.addEventListener("touchmove", drag, false);
                canvas.addEventListener("touchend", release, false);
                canvas.addEventListener("touchcancel", cancel, false);
            };

            // Resource loaded callback
            var resourceLoaded = function () {
                curLoadResNum += 1;
                if (curLoadResNum === totalLoadResources) {
                    redraw();
                    createUserEvents();
                }
            };

            // Setup color palette
            var setupColors = function() {
                var colorPalette = document.getElementById('colorPalette');
                colors.forEach(function(color, index) {
                    var colorDiv = document.createElement('div');
                    colorDiv.className = 'color-option1';
                    colorDiv.style.backgroundColor = color;
                    colorDiv.id = 'color' + (index + 1);
                    if (index === 9) colorDiv.classList.add('active'); // Default green
                    colorPalette.appendChild(colorDiv);
                });
            };

            // Setup event listeners
            var setupEventListeners = function() {
                // Color selection
                $(".color-option1").click(function () {
                    $(".color-option1").removeClass('active');
                    $(this).addClass('active');
                    var index = parseInt(this.id.replace('color', '')) - 1;
                    curColor = colors[index];
                });

                // Tool selection
                $(".color-option2").click(function () {
                    $(".color-option2").removeClass('toolActive');
                    $(this).addClass('toolActive');
                    curTool = this.id;

                    if (curTool === "clear") {
                        if (confirm('Bạn có chắc muốn xóa hết không?')) {
                            context.clearRect(0, 0, canvasWidth, canvasHeight);
                            context.drawImage(outlineImage, 0, 0, canvasWidth, canvasHeight);
                            clickX = [];
                            clickY = [];
                            clickDrag = [];
                            clickTool = [];
                            clickColor = [];
                            clickSize = [];
                        }
                        curTool = "marker";
                        $("#marker").addClass('toolActive');
                        $(this).removeClass('toolActive');
                    } else if (curTool === "undo") {
                        redraw();
                        curTool = "marker";
                        $("#marker").addClass('toolActive');
                        $(this).removeClass('toolActive');
                    }
                });

                // Size selection
                $(".itemSize").click(function () {
                    $(".itemSize").removeClass('sizeActive');
                    $(this).addClass('sizeActive');
                    curSize = $(this).data('size');
                });

                // Download button
                $('#download').click(function() {
                    setSave();
                });
            };

            // Initialize the application
            var init = function () {
                // Setup responsive canvas
                var imageW = 1027;
                var imageH = 787;
                var screenW = $('.container').width();
                var canvasW, canvasH;

                if(screenW < 700) {
                    canvasW = (95 * screenW) / 100;
                } else {
                    canvasW = 700;
                }

                canvasH = (canvasW / imageW) * imageH;
                canvasWidth = canvasW;
                canvasHeight = canvasH;
                drawingAreaWidth = canvasW;
                drawingAreaHeight = canvasH;

                // Setup canvas
                canvas = document.getElementById("canvas");
                canvas.setAttribute('width', canvasWidth);
                canvas.setAttribute('height', canvasHeight);

                if (typeof G_vmlCanvasManager !== "undefined") {
                    canvas = G_vmlCanvasManager.initElement(canvas);
                }

                context = canvas.getContext("2d");

                // Setup canvas container size
                $("#canvasDiv").css({
                    "width": canvasW + 5,
                    "height": canvasH + 5
                });

                // Setup UI
                setupColors();
                setupEventListeners();

                // Load capybara image
                outlineImage.onload = resourceLoaded;
                outlineImage.src = url;
            };

            return {
                init: init
            };
        }());

        // Initialize when document is ready
        $(document).ready(function() {
            drawingApp.init();
        });
    </script>
</body>
</html>
