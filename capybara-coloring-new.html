<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capybara Coloring App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .control-row {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }

        .control-row:last-child {
            margin-bottom: 0;
        }

        .control-row label {
            font-weight: bold;
            color: #555;
            margin-right: 10px;
            min-width: 80px;
        }

        /* Color Palette */
        .color-palette {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            justify-content: center;
            max-width: 800px;
        }

        .color-option1 {
            width: 40px;
            height: 40px;
            border: 3px solid transparent;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option1:hover {
            transform: scale(1.1);
            border-color: #333;
        }

        .color-option1.active {
            border-color: #000;
            transform: scale(1.2);
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }

        /* Tools */
        .tools {
            display: flex;
            gap: 10px;
        }

        .color-option2 {
            padding: 10px 15px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .color-option2:hover {
            background: #f0f0f0;
            border-color: #999;
        }

        .color-option2.toolActive {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        /* Brush Sizes */
        .sizes {
            display: flex;
            gap: 10px;
        }

        .itemSize {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            transition: all 0.3s ease;
        }

        .itemSize:hover {
            border-color: #999;
            background: #f0f0f0;
        }

        .itemSize.sizeActive {
            border-color: #007bff;
            background: #007bff;
        }

        .itemSize::before {
            content: '';
            background: #333;
            border-radius: 50%;
        }

        .itemSize[data-size="least"]::before { width: 4px; height: 4px; }
        .itemSize[data-size="small"]::before { width: 8px; height: 8px; }
        .itemSize[data-size="medium"]::before { width: 12px; height: 12px; }
        .itemSize[data-size="large"]::before { width: 16px; height: 16px; }
        .itemSize[data-size="huge"]::before { width: 20px; height: 20px; }

        .itemSize.sizeActive::before {
            background: white;
        }

        /* Canvas Container */
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }

        #canvasDiv {
            display: inline-block;
            border: 3px solid #333;
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        canvas {
            display: block;
            border-radius: 7px;
            cursor: none; /* Hide default cursor */
        }

        /* Download Button */
        #download {
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            transition: background 0.3s ease;
            margin-left: 15px;
        }

        #download:hover {
            background: #218838;
        }

        /* Image Selection Popup */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .popup-content {
            background: white;
            border-radius: 15px;
            max-width: 600px;
            max-height: 80vh;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            display: flex;
            flex-direction: column;
        }

        .popup-header {
            text-align: center;
            padding: 20px 30px 0 30px;
            flex-shrink: 0;
        }

        .popup-header h2 {
            color: #333;
            margin: 0;
        }

        .close-popup {
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 24px;
            cursor: pointer;
            color: #999;
            background: none;
            border: none;
        }

        .close-popup:hover {
            color: #333;
        }

        .popup-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px 30px;
        }

        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .image-option {
            border: 3px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
            background: #f8f9fa;
        }

        .image-option:hover {
            border-color: #007bff;
            transform: scale(1.05);
        }

        .image-option.selected {
            border-color: #28a745;
            transform: scale(1.05);
        }

        .image-option img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            display: block;
        }

        .image-option .image-name {
            padding: 10px;
            text-align: center;
            font-weight: bold;
            color: #555;
            font-size: 12px;
        }

        .popup-footer {
            padding: 0 30px 20px 30px;
            flex-shrink: 0;
        }

        .popup-buttons {
            text-align: center;
            gap: 15px;
            display: flex;
            justify-content: center;
        }

        .popup-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s ease;
        }

        .popup-btn.primary {
            background: #007bff;
            color: white;
        }

        .popup-btn.primary:hover {
            background: #0056b3;
        }

        .popup-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .popup-btn.secondary:hover {
            background: #545b62;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .color-palette {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦫 Ứng Dụng Tô Màu Capybara</h1>
        
        <div class="controls">
            <!-- Hàng 1: Color Palette -->
            <div class="control-row">
                <label>Chọn Màu:</label>
                <div class="color-palette" id="colorPalette">
                    <!-- Colors will be generated by JavaScript -->
                </div>
            </div>

            <!-- Hàng 2: Brush Sizes -->
            <div class="control-row">
                <label>Kích Thước:</label>
                <div class="sizes">
                    <div class="itemSize" data-size="least" id="least"></div>
                    <div class="itemSize sizeActive" data-size="small" id="small"></div>
                    <div class="itemSize" data-size="medium" id="medium"></div>
                    <div class="itemSize" data-size="large" id="large"></div>
                    <div class="itemSize" data-size="huge" id="huge"></div>
                </div>
            </div>

            <!-- Hàng 3: Tools và Save -->
            <div class="control-row">
                <label>Công Cụ:</label>
                <div class="tools">
                    <div class="color-option2" id="selectImage">🖼️ Chọn Hình</div>
                    <div class="color-option2 toolActive" id="marker">Bút Vẽ</div>
                    <div class="color-option2" id="eraser">Tẩy</div>
                    <div class="color-option2" id="clear">Xóa Hết</div>
                    <div class="color-option2" id="undo">Hoàn Tác</div>
                </div>
                <a href="#" id="download" download="capybara-coloring.png">💾 Tải Xuống</a>
            </div>
        </div>

        <!-- Canvas -->
        <div class="canvas-container">
            <div id="canvasDiv">
                <canvas id="canvas"></canvas>
            </div>
        </div>
    </div>

    <!-- Image Selection Popup -->
    <div class="popup-overlay" id="imagePopup">
        <div class="popup-content">
            <button class="close-popup" id="closePopup">&times;</button>
            <div class="popup-header">
                <h2>🎨 Chọn Hình Để Tô Màu</h2>
            </div>
            <div class="popup-body">
                <div class="image-grid" id="imageGrid">
                    <!-- Images will be populated by JavaScript -->
                </div>
            </div>
            <div class="popup-footer">
                <div class="popup-buttons">
                    <button class="popup-btn primary" id="loadSelectedImage">Tải Hình</button>
                    <button class="popup-btn secondary" id="cancelSelection">Hủy</button>
                </div>
            </div>
        </div>


    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        var drawingApp = (function () {
            "use strict";
            
            var canvas,
                context,
                // 36 màu như code mẫu
                colors = [
                    "#F90B01", "#B51A01", "#A84D02", "#F98001", "#F9BC7E", "#F9F97E",
                    "#E4E20B", "#A3BC08", "#90FC84", "#03CB01", "#5B9908", "#4E4C01",
                    "#013D01", "#8BEDDC", "#43AEEB", "#8F9EF9", "#0D30F9", "#13229E",
                    "#01083F", "#480143", "#780F8F", "#6F06C1", "#C58EFB", "#F9B7CE",
                    "#F9656F", "#A10151", "#5B0101", "#6E3511", "#674733", "#BCA383",
                    "#FFFF00", "#858585", "#3F3F3F", "#000000", "#DF0029", "#00B2BF"
                ],
                outlineImage = new Image(),
                clickX = [],
                clickY = [],
                clickColor = [],
                clickTool = [],
                clickSize = [],
                clickDrag = [],
                strokes = [], // Lưu từng nét vẽ riêng biệt
                paint = false,
                curColor = colors[9], // Default green
                curTool = "marker",
                curSize = "small",
                currentStroke = null, // Track current stroke being drawn
                customCursor = null, // Custom cursor element
                canvasWidth,
                canvasHeight,
                drawingAreaX = 0,
                drawingAreaY = 0,
                drawingAreaWidth,
                drawingAreaHeight,
                totalLoadResources = 1,
                curLoadResNum = 0,
                downloadImg = 'capybara-coloring';

            var url = 'Tranh-To-Mau-Capybara-Nho.jpg';

            // Available images for selection
            var availableImages = [
                { name: 'Capybara Nhỏ', src: 'Tranh-To-Mau-Capybara-Nho.jpg' },
                { name: 'Capybara Lớn', src: 'Tranh-To-Mau-Capybara-va-Cay-Dua.jpg' },
                { name: 'Capybara Cute', src: 'capybara-cute.jpg' },
                { name: 'Capybara Family', src: 'capybara-family.jpg' }
            ];

            var selectedImageIndex = 0;

            // Clear canvas
            var clearCanvas = function () {
                context.clearRect(0, 0, canvasWidth, canvasHeight);
            };

            // Set save function
            var setSave = function() {
                try {
                    // Create a new canvas with white background to avoid transparency issues
                    var tempCanvas = document.createElement('canvas');
                    var tempCtx = tempCanvas.getContext('2d');
                    tempCanvas.width = canvas.width;
                    tempCanvas.height = canvas.height;

                    // Fill with white background
                    tempCtx.fillStyle = 'white';
                    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                    // Draw the original canvas on top
                    tempCtx.drawImage(canvas, 0, 0);

                    // Convert to blob and create download link
                    tempCanvas.toBlob(function(blob) {
                        var url = URL.createObjectURL(blob);
                        var save = document.getElementById('download');
                        save.href = url;
                        save.download = downloadImg + ".png";

                        // Clean up the URL after download
                        save.onclick = function() {
                            setTimeout(function() {
                                URL.revokeObjectURL(url);
                            }, 100);
                        };
                    }, 'image/png', 1.0);
                } catch (error) {
                    console.error('Cannot export canvas:', error);
                    alert('Không thể tải xuống hình ảnh. Vui lòng thử lại.');
                }
            };

            // Redraw canvas
            var redraw = function () {
                var locX, locY, radius, i;

                // Make sure required resources are loaded before redrawing
                if (curLoadResNum < totalLoadResources) {
                    return;
                }

                clearCanvas();

                // Handle special tools
                if (curTool === "undo") {
                    // Remove last complete stroke
                    if (strokes.length > 0) {
                        var lastStroke = strokes.pop();
                        // Remove all points of the last stroke
                        for (var i = 0; i < lastStroke.length; i++) {
                            clickX.pop();
                            clickY.pop();
                            clickDrag.pop();
                            clickTool.pop();
                            clickColor.pop();
                            clickSize.pop();
                        }
                    }
                    curTool = "marker"; // Reset to marker after undo
                }

                // Keep the drawing in the drawing area
                context.save();
                context.beginPath();
                context.rect(drawingAreaX, drawingAreaY, drawingAreaWidth, drawingAreaHeight);
                context.clip();

                // For each point drawn
                for (i = 0; i < clickX.length; i += 1) {
                    // Set the drawing radius based on size
                    switch (clickSize[i]) {
                        case "least": radius = 2; break;
                        case "small": radius = 5; break;
                        case "medium": radius = 10; break;
                        case "large": radius = 15; break;
                        case "huge": radius = 25; break;
                        default: radius = 5; break;
                    }

                    // Set the drawing path
                    context.beginPath();
                    // If dragging then draw a line between the two points
                    if (clickDrag[i] && i) {
                        context.moveTo(clickX[i - 1], clickY[i - 1]);
                    } else {
                        // The x position is moved over one pixel so a circle even if not dragging
                        context.moveTo(clickX[i] - 1, clickY[i]);
                    }
                    context.lineTo(clickX[i], clickY[i]);

                    // Set the drawing color
                    if (clickTool[i] === "eraser") {
                        context.strokeStyle = 'white';
                    } else {
                        context.strokeStyle = clickColor[i];
                    }
                    context.lineCap = "round";
                    context.lineJoin = "round";
                    context.lineWidth = radius;
                    context.stroke();
                }
                context.closePath();
                context.restore();

                // Draw the outline image with multiply mode (key technique from sample code)
                context.globalCompositeOperation = "multiply";
                context.drawImage(outlineImage, drawingAreaX, drawingAreaY, drawingAreaWidth, drawingAreaHeight);
                context.globalCompositeOperation = "source-over";
            };

            // Add a point to the drawing array
            var addClick = function (x, y, dragging) {
                clickX.push(x);
                clickY.push(y);
                clickTool.push(curTool);
                clickColor.push(curColor);
                clickSize.push(curSize);
                clickDrag.push(dragging);

                // Track current stroke
                if (!dragging) {
                    // Start new stroke
                    currentStroke = [];
                    strokes.push(currentStroke);
                }
                if (strokes.length > 0) {
                    currentStroke.push(clickX.length - 1); // Store index of this point
                }
            };

            // Create custom cursor
            var createCustomCursor = function() {
                customCursor = document.createElement('div');
                customCursor.style.position = 'fixed';
                customCursor.style.pointerEvents = 'none';
                customCursor.style.borderRadius = '50%';
                customCursor.style.border = '1px solid #333';
                customCursor.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                customCursor.style.zIndex = '9999';
                customCursor.style.transform = 'translate(-50%, -50%)';
                customCursor.style.display = 'none';
                customCursor.style.margin = '0';
                customCursor.style.padding = '0';
                customCursor.style.boxSizing = 'border-box';
                document.body.appendChild(customCursor);
                updateCursorSize();
            };

            // Update cursor size based on current brush size
            var updateCursorSize = function() {
                if (!customCursor) return;

                var radius;
                switch (curSize) {
                    case "least": radius = 2; break;
                    case "small": radius = 5; break;
                    case "medium": radius = 10; break;
                    case "large": radius = 15; break;
                    case "huge": radius = 25; break;
                    default: radius = 5; break;
                }

                // Canvas scale factor
                var canvasScale = canvas.offsetWidth / canvas.width;

                // Cursor should show the actual paint area
                // Canvas uses lineWidth = radius, which creates a line of width = radius
                // So cursor diameter should = radius * scale (not radius * 2)
                var actualPaintDiameter = radius * canvasScale;

                // Set cursor to match actual paint size
                customCursor.style.width = actualPaintDiameter + 'px';
                customCursor.style.height = actualPaintDiameter + 'px';
                customCursor.style.backgroundColor = curColor + '40'; // Add transparency
                customCursor.style.border = '1px solid #333';
            };

            // Show/hide custom cursor
            var showCustomCursor = function(x, y) {
                if (customCursor) {
                    customCursor.style.display = 'block';
                    customCursor.style.left = x + 'px';
                    customCursor.style.top = y + 'px';
                }
            };

            var hideCustomCursor = function() {
                if (customCursor) {
                    customCursor.style.display = 'none';
                }
            };

            // Create user events
            var createUserEvents = function () {
                var press = function (e) {
                    var mouseX = (e.changedTouches ? e.changedTouches[0].pageX : e.pageX) - this.offsetLeft,
                        mouseY = (e.changedTouches ? e.changedTouches[0].pageY : e.pageY) - this.offsetTop;

                    paint = true;
                    addClick(mouseX, mouseY, false);
                    redraw();
                };

                var drag = function (e) {
                    var mouseX = (e.changedTouches ? e.changedTouches[0].pageX : e.pageX) - this.offsetLeft,
                        mouseY = (e.changedTouches ? e.changedTouches[0].pageY : e.pageY) - this.offsetTop;

                    // Update cursor position - use canvas offset + mouse position
                    var canvasRect = canvas.getBoundingClientRect();
                    var globalX = canvasRect.left + mouseX;
                    var globalY = canvasRect.top + mouseY;
                    showCustomCursor(globalX, globalY);

                    if (paint) {
                        addClick(mouseX, mouseY, true);
                        redraw();
                    }
                    // Prevent the whole page from dragging if on mobile
                    e.preventDefault();
                };

                var release = function () {
                    paint = false;
                    redraw();
                };

                var cancel = function () {
                    paint = false;
                    hideCustomCursor();
                };

                // Mouse enter/leave events for cursor
                var mouseEnter = function(e) {
                    var mouseX = e.pageX - this.offsetLeft;
                    var mouseY = e.pageY - this.offsetTop;
                    var canvasRect = canvas.getBoundingClientRect();
                    var globalX = canvasRect.left + mouseX;
                    var globalY = canvasRect.top + mouseY;
                    showCustomCursor(globalX, globalY);
                };

                var mouseLeave = function() {
                    hideCustomCursor();
                };

                // Mouse move event for cursor tracking (when not dragging)
                var mouseMove = function(e) {
                    if (!paint) { // Only update cursor when not painting
                        var mouseX = (e.changedTouches ? e.changedTouches[0].pageX : e.pageX) - this.offsetLeft;
                        var mouseY = (e.changedTouches ? e.changedTouches[0].pageY : e.pageY) - this.offsetTop;
                        var canvasRect = canvas.getBoundingClientRect();
                        var globalX = canvasRect.left + mouseX;
                        var globalY = canvasRect.top + mouseY;
                        showCustomCursor(globalX, globalY);
                    }
                };

                // Add mouse event listeners to canvas element
                canvas.addEventListener("mousedown", press, false);
                canvas.addEventListener("mousemove", function(e) {
                    // Call both drag (for drawing) and mouseMove (for cursor)
                    drag.call(this, e);
                    if (!paint) {
                        mouseMove.call(this, e);
                    }
                }, false);
                canvas.addEventListener("mouseup", release);
                canvas.addEventListener("mouseout", cancel, false);
                canvas.addEventListener("mouseenter", mouseEnter, false);
                canvas.addEventListener("mouseleave", mouseLeave, false);

                // Add touch event listeners to canvas element
                canvas.addEventListener("touchstart", press, false);
                canvas.addEventListener("touchmove", drag, false);
                canvas.addEventListener("touchend", release, false);
                canvas.addEventListener("touchcancel", cancel, false);
            };

            // Resource loaded callback
            var resourceLoaded = function () {
                curLoadResNum += 1;
                if (curLoadResNum === totalLoadResources) {
                    redraw();
                    createUserEvents();
                }
            };

            // Setup color palette
            var setupColors = function() {
                var colorPalette = document.getElementById('colorPalette');
                colors.forEach(function(color, index) {
                    var colorDiv = document.createElement('div');
                    colorDiv.className = 'color-option1';
                    colorDiv.style.backgroundColor = color;
                    colorDiv.id = 'color' + (index + 1);
                    if (index === 9) colorDiv.classList.add('active'); // Default green
                    colorPalette.appendChild(colorDiv);
                });
            };

            // Setup image popup
            var setupImagePopup = function() {
                var imageGrid = document.getElementById('imageGrid');
                imageGrid.innerHTML = '';

                availableImages.forEach(function(image, index) {
                    var imageDiv = document.createElement('div');
                    imageDiv.className = 'image-option';
                    if (index === selectedImageIndex) {
                        imageDiv.classList.add('selected');
                    }
                    imageDiv.setAttribute('data-index', index);

                    imageDiv.innerHTML = `
                        <img src="${image.src}" alt="${image.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='">
                        <div class="image-name">${image.name}</div>
                    `;

                    imageDiv.addEventListener('click', function() {
                        document.querySelectorAll('.image-option').forEach(function(el) {
                            el.classList.remove('selected');
                        });
                        this.classList.add('selected');
                        selectedImageIndex = parseInt(this.getAttribute('data-index'));
                    });

                    imageGrid.appendChild(imageDiv);
                });
            };

            // Show image popup
            var showImagePopup = function() {
                setupImagePopup();
                document.getElementById('imagePopup').style.display = 'flex';
            };

            // Hide image popup
            var hideImagePopup = function() {
                document.getElementById('imagePopup').style.display = 'none';
            };

            // Load selected image
            var loadSelectedImage = function() {
                var selectedImage = availableImages[selectedImageIndex];
                url = selectedImage.src;

                // Clear current canvas
                context.clearRect(0, 0, canvasWidth, canvasHeight);
                clickX = [];
                clickY = [];
                clickDrag = [];
                clickTool = [];
                clickColor = [];
                clickSize = [];
                strokes = [];

                // Load new image
                outlineImage.onload = function() {
                    context.drawImage(outlineImage, 0, 0, canvasWidth, canvasHeight);
                };
                outlineImage.src = url;

                hideImagePopup();
            };

            // Setup event listeners
            var setupEventListeners = function() {
                // Color selection
                $(".color-option1").click(function () {
                    $(".color-option1").removeClass('active');
                    $(this).addClass('active');
                    var index = parseInt(this.id.replace('color', '')) - 1;
                    curColor = colors[index];
                    updateCursorSize(); // Update cursor color when color changes
                });

                // Tool selection
                $(".color-option2").click(function () {
                    var toolId = this.id;

                    if (toolId === "selectImage") {
                        showImagePopup();
                        return; // Don't change active tool for image selection
                    }

                    $(".color-option2").removeClass('toolActive');
                    $(this).addClass('toolActive');
                    curTool = toolId;

                    if (curTool === "clear") {
                        if (confirm('Bạn có chắc muốn xóa hết không?')) {
                            context.clearRect(0, 0, canvasWidth, canvasHeight);
                            context.drawImage(outlineImage, 0, 0, canvasWidth, canvasHeight);
                            clickX = [];
                            clickY = [];
                            clickDrag = [];
                            clickTool = [];
                            clickColor = [];
                            clickSize = [];
                            strokes = []; // Clear strokes too
                        }
                        curTool = "marker";
                        $("#marker").addClass('toolActive');
                        $(this).removeClass('toolActive');
                    } else if (curTool === "undo") {
                        redraw();
                        curTool = "marker";
                        $("#marker").addClass('toolActive');
                        $(this).removeClass('toolActive');
                    }
                });

                // Size selection
                $(".itemSize").click(function () {
                    $(".itemSize").removeClass('sizeActive');
                    $(this).addClass('sizeActive');
                    curSize = $(this).data('size');
                    updateCursorSize(); // Update cursor when size changes
                });

                // Download button
                $('#download').click(function() {
                    setSave();
                });

                // Popup event listeners
                $('#closePopup, #cancelSelection').click(function() {
                    hideImagePopup();
                });

                $('#loadSelectedImage').click(function() {
                    loadSelectedImage();
                });

                // Close popup when clicking outside
                $('#imagePopup').click(function(e) {
                    if (e.target === this) {
                        hideImagePopup();
                    }
                });
            };

            // Initialize the application
            var init = function () {
                // Setup responsive canvas
                var imageW = 1027;
                var imageH = 787;
                var screenW = $('.container').width();
                var canvasW, canvasH;

                if(screenW < 700) {
                    canvasW = (95 * screenW) / 100;
                } else {
                    canvasW = 700;
                }

                canvasH = (canvasW / imageW) * imageH;
                canvasWidth = canvasW;
                canvasHeight = canvasH;
                drawingAreaWidth = canvasW;
                drawingAreaHeight = canvasH;

                // Setup canvas
                canvas = document.getElementById("canvas");
                canvas.setAttribute('width', canvasWidth);
                canvas.setAttribute('height', canvasHeight);

                if (typeof G_vmlCanvasManager !== "undefined") {
                    canvas = G_vmlCanvasManager.initElement(canvas);
                }

                context = canvas.getContext("2d");

                // Setup canvas container size
                $("#canvasDiv").css({
                    "width": canvasW + 5,
                    "height": canvasH + 5
                });

                // Setup UI
                setupColors();
                createCustomCursor();
                setupEventListeners();

                // Load capybara image
                outlineImage.onload = resourceLoaded;
                outlineImage.src = url;
            };

            return {
                init: init
            };
        }());

        // Initialize when document is ready
        $(document).ready(function() {
            drawingApp.init();
        });
    </script>
</body>
</html>
