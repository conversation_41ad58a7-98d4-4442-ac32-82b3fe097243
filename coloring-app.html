<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON> M<PERSON>u Capybara</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
            justify-content: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        .color-picker {
            width: 60px;
            height: 60px;
            border: 3px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .color-picker:hover {
            transform: scale(1.1);
            border-color: #667eea;
        }

        .size-slider {
            width: 150px;
            height: 8px;
            border-radius: 4px;
            background: #ddd;
            outline: none;
            cursor: pointer;
        }

        .size-display {
            background: #f0f0f0;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
            color: #333;
            min-width: 60px;
            text-align: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .btn-return {
            background: #4CAF50;
            color: white;
        }

        .btn-return:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-clear {
            background: #f44336;
            color: white;
        }

        .btn-clear:hover {
            background: #da190b;
            transform: translateY(-2px);
        }

        .btn-save {
            background: #2196F3;
            color: white;
        }

        .btn-save:hover {
            background: #1976D2;
            transform: translateY(-2px);
        }

        .canvas-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            position: relative;
        }

        #coloringCanvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            cursor: crosshair;
            display: block;
            width: 600px;
            height: 600px;
            max-width: 100%;
        }

        .brush-preview {
            position: absolute;
            border: 2px solid #333;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }

        .color-palette {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 10px;
        }

        .preset-color {
            width: 40px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 50%;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .preset-color:hover {
            transform: scale(1.1);
            border-color: #333;
        }

        .preset-color.active {
            border-color: #667eea;
            border-width: 3px;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                gap: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .canvas-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Tô Màu Capybara</h1>
        <p>Hãy tô màu cho chú capybara dễ thương!</p>
    </div>

    <div class="controls">
        <div class="control-group">
            <label>Chọn Màu</label>
            <input type="color" id="colorPicker" class="color-picker" value="#ff0000">
        </div>

        <div class="control-group">
            <label>Kích Thước Cọ</label>
            <input type="range" id="brushSize" class="size-slider" min="5" max="50" value="15">
            <div class="size-display" id="sizeDisplay">15px</div>
        </div>

        <div class="control-group">
            <label>Thao Tác</label>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-return" id="returnBtn">↶ Hoàn Tác</button>
                <button class="btn btn-clear" id="clearBtn">🗑 Xóa Hết</button>
                <button class="btn btn-save" id="saveBtn">💾 Lưu Hình</button>
            </div>
        </div>
    </div>

    <div class="color-palette">
        <div class="preset-color" style="background-color: #ff0000;" data-color="#ff0000"></div>
        <div class="preset-color" style="background-color: #00ff00;" data-color="#00ff00"></div>
        <div class="preset-color" style="background-color: #0000ff;" data-color="#0000ff"></div>
        <div class="preset-color" style="background-color: #ffff00;" data-color="#ffff00"></div>
        <div class="preset-color" style="background-color: #ff00ff;" data-color="#ff00ff"></div>
        <div class="preset-color" style="background-color: #00ffff;" data-color="#00ffff"></div>
        <div class="preset-color" style="background-color: #ffa500;" data-color="#ffa500"></div>
        <div class="preset-color" style="background-color: #800080;" data-color="#800080"></div>
        <div class="preset-color" style="background-color: #ffc0cb;" data-color="#ffc0cb"></div>
        <div class="preset-color" style="background-color: #8b4513;" data-color="#8b4513"></div>
    </div>

    <div class="canvas-container">
        <canvas id="coloringCanvas" width="600" height="600"></canvas>
        <div class="brush-preview" id="brushPreview"></div>
    </div>

    <script>
        class ColoringApp {
            constructor() {
                this.canvas = document.getElementById('coloringCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.colorPicker = document.getElementById('colorPicker');
                this.brushSize = document.getElementById('brushSize');
                this.sizeDisplay = document.getElementById('sizeDisplay');
                this.returnBtn = document.getElementById('returnBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.saveBtn = document.getElementById('saveBtn');
                this.brushPreview = document.getElementById('brushPreview');
                
                this.isDrawing = false;
                this.history = [];
                this.historyStep = -1;
                this.capybaraImage = null;
                this.originalImageData = null;
                this.outlineImageData = null;

                this.init();
            }

            init() {
                this.loadCapybaraImage();
                this.setupEventListeners();
                this.saveState();
            }

            loadCapybaraImage() {
                // Tạo hình capybara đơn giản bằng code (bạn có thể thay bằng hình thật)
                this.drawCapybaraOutline();
                this.originalImageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);

                // Lưu riêng data của outline để vẽ lại sau
                this.saveOutlineData();
            }

            saveOutlineData() {
                // Tạo canvas tạm để lưu chỉ outline
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height;

                // Vẽ lại outline trên canvas tạm
                this.drawCapybaraOutlineOnly(tempCtx);
                this.outlineImageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
            }

            drawCapybaraOutlineOnly(ctx) {
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 3;
                ctx.fillStyle = 'transparent';

                // Vẽ thân capybara (chỉ viền)
                ctx.beginPath();
                ctx.ellipse(300, 350, 150, 100, 0, 0, 2 * Math.PI);
                ctx.stroke();

                // Vẽ đầu (chỉ viền)
                ctx.beginPath();
                ctx.ellipse(300, 200, 80, 70, 0, 0, 2 * Math.PI);
                ctx.stroke();

                // Vẽ tai (chỉ viền)
                ctx.beginPath();
                ctx.ellipse(260, 160, 20, 30, -0.3, 0, 2 * Math.PI);
                ctx.stroke();

                ctx.beginPath();
                ctx.ellipse(340, 160, 20, 30, 0.3, 0, 2 * Math.PI);
                ctx.stroke();

                // Vẽ mắt
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(280, 190, 8, 0, 2 * Math.PI);
                ctx.fill();

                ctx.beginPath();
                ctx.arc(320, 190, 8, 0, 2 * Math.PI);
                ctx.fill();

                // Vẽ mũi
                ctx.beginPath();
                ctx.ellipse(300, 210, 6, 4, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Vẽ chân (chỉ viền)
                ctx.strokeStyle = 'black';

                // Chân trước
                ctx.beginPath();
                ctx.ellipse(250, 420, 25, 40, 0, 0, 2 * Math.PI);
                ctx.stroke();

                ctx.beginPath();
                ctx.ellipse(350, 420, 25, 40, 0, 0, 2 * Math.PI);
                ctx.stroke();

                // Chân sau
                ctx.beginPath();
                ctx.ellipse(220, 400, 20, 35, 0, 0, 2 * Math.PI);
                ctx.stroke();

                ctx.beginPath();
                ctx.ellipse(380, 400, 20, 35, 0, 0, 2 * Math.PI);
                ctx.stroke();
            }

            drawCapybaraOutline() {
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                this.ctx.strokeStyle = 'black';
                this.ctx.lineWidth = 3;
                this.ctx.fillStyle = 'white';

                // Vẽ thân capybara
                this.ctx.beginPath();
                this.ctx.ellipse(300, 350, 150, 100, 0, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                // Vẽ đầu
                this.ctx.beginPath();
                this.ctx.ellipse(300, 200, 80, 70, 0, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                // Vẽ tai
                this.ctx.beginPath();
                this.ctx.ellipse(260, 160, 20, 30, -0.3, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                this.ctx.beginPath();
                this.ctx.ellipse(340, 160, 20, 30, 0.3, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                // Vẽ mắt
                this.ctx.fillStyle = 'black';
                this.ctx.beginPath();
                this.ctx.arc(280, 190, 8, 0, 2 * Math.PI);
                this.ctx.fill();

                this.ctx.beginPath();
                this.ctx.arc(320, 190, 8, 0, 2 * Math.PI);
                this.ctx.fill();

                // Vẽ mũi
                this.ctx.beginPath();
                this.ctx.ellipse(300, 210, 6, 4, 0, 0, 2 * Math.PI);
                this.ctx.fill();

                // Vẽ chân
                this.ctx.fillStyle = 'white';
                this.ctx.strokeStyle = 'black';
                
                // Chân trước
                this.ctx.beginPath();
                this.ctx.ellipse(250, 420, 25, 40, 0, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                this.ctx.beginPath();
                this.ctx.ellipse(350, 420, 25, 40, 0, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                // Chân sau
                this.ctx.beginPath();
                this.ctx.ellipse(220, 400, 20, 35, 0, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();

                this.ctx.beginPath();
                this.ctx.ellipse(380, 400, 20, 35, 0, 0, 2 * Math.PI);
                this.ctx.fill();
                this.ctx.stroke();
            }

            setupEventListeners() {
                // Canvas events
                this.canvas.addEventListener('mousedown', (e) => this.startDrawing(e));
                this.canvas.addEventListener('mousemove', (e) => this.draw(e));
                this.canvas.addEventListener('mouseup', () => this.stopDrawing());
                this.canvas.addEventListener('mouseout', () => this.stopDrawing());

                // Touch events for mobile
                this.canvas.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    const mouseEvent = new MouseEvent('mousedown', {
                        clientX: touch.clientX,
                        clientY: touch.clientY
                    });
                    this.canvas.dispatchEvent(mouseEvent);
                });

                this.canvas.addEventListener('touchmove', (e) => {
                    e.preventDefault();
                    const touch = e.touches[0];
                    const mouseEvent = new MouseEvent('mousemove', {
                        clientX: touch.clientX,
                        clientY: touch.clientY
                    });
                    this.canvas.dispatchEvent(mouseEvent);
                });

                this.canvas.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    const mouseEvent = new MouseEvent('mouseup', {});
                    this.canvas.dispatchEvent(mouseEvent);
                });

                // Brush preview
                this.canvas.addEventListener('mousemove', (e) => this.updateBrushPreview(e));
                this.canvas.addEventListener('mouseenter', () => {
                    this.brushPreview.style.display = 'block';
                });
                this.canvas.addEventListener('mouseleave', () => {
                    this.brushPreview.style.display = 'none';
                });

                // Controls
                this.brushSize.addEventListener('input', () => {
                    this.sizeDisplay.textContent = this.brushSize.value + 'px';
                });

                this.returnBtn.addEventListener('click', () => this.undo());
                this.clearBtn.addEventListener('click', () => this.clearCanvas());
                this.saveBtn.addEventListener('click', () => this.saveImage());

                // Preset colors
                document.querySelectorAll('.preset-color').forEach(color => {
                    color.addEventListener('click', (e) => {
                        const colorValue = e.target.dataset.color;
                        this.colorPicker.value = colorValue;
                        
                        // Update active state
                        document.querySelectorAll('.preset-color').forEach(c => c.classList.remove('active'));
                        e.target.classList.add('active');
                    });
                });
            }

            getMousePos(e) {
                const rect = this.canvas.getBoundingClientRect();
                const scaleX = this.canvas.width / rect.width;
                const scaleY = this.canvas.height / rect.height;

                const x = (e.clientX - rect.left) * scaleX;
                const y = (e.clientY - rect.top) * scaleY;

                // Debug log để kiểm tra tọa độ
                console.log('Mouse pos:', { x, y, clientX: e.clientX, clientY: e.clientY, rect });

                return { x, y };
            }

            isInsideCapybara(x, y) {
                const imageData = this.originalImageData;
                const index = (Math.floor(y) * imageData.width + Math.floor(x)) * 4;

                if (index < 0 || index >= imageData.data.length) return false;

                // Kiểm tra xem pixel có phải là vùng trắng (bên trong) không
                const r = imageData.data[index];
                const g = imageData.data[index + 1];
                const b = imageData.data[index + 2];

                // CHỈ cho phép tô trong vùng trắng hoàn toàn (KHÔNG phải viền đen)
                // Viền đen có RGB < 50, vùng trắng có RGB > 240
                return r > 240 && g > 240 && b > 240;
            }

            canDrawAt(x, y, brushSize) {
                // Kiểm tra toàn bộ vùng brush để đảm bảo không chạm viền
                const radius = Math.ceil(brushSize / 2);

                for (let dx = -radius; dx <= radius; dx++) {
                    for (let dy = -radius; dy <= radius; dy++) {
                        const distance = Math.sqrt(dx * dx + dy * dy);
                        if (distance <= radius) {
                            const checkX = x + dx;
                            const checkY = y + dy;

                            // Nếu có bất kỳ pixel nào trong vùng brush chạm viền
                            if (!this.isInsideCapybara(checkX, checkY)) {
                                return false;
                            }
                        }
                    }
                }
                return true;
            }

            redrawOutline() {
                // Vẽ lại outline lên trên màu đã tô bằng cách vẽ lại các đường viền
                this.ctx.save();

                // Set composite operation để viền luôn hiển thị trên cùng
                this.ctx.globalCompositeOperation = 'source-over';

                this.drawCapybaraOutlineOnly(this.ctx);
                this.ctx.restore();
            }

            startDrawing(e) {
                const pos = this.getMousePos(e);

                console.log('Starting to draw at:', pos);

                this.isDrawing = true;

                // Bắt đầu path mới và vẽ điểm đầu tiên
                this.ctx.globalCompositeOperation = 'source-over';
                this.ctx.lineCap = 'round';
                this.ctx.lineJoin = 'round';
                this.ctx.strokeStyle = this.colorPicker.value;
                this.ctx.lineWidth = this.brushSize.value;

                console.log('Brush settings:', {
                    color: this.colorPicker.value,
                    size: this.brushSize.value
                });

                this.ctx.beginPath();
                this.ctx.moveTo(pos.x, pos.y);

                // Vẽ điểm đầu tiên
                this.ctx.lineTo(pos.x, pos.y);
                this.ctx.stroke();
            }

            draw(e) {
                if (!this.isDrawing) return;

                const pos = this.getMousePos(e);

                // Cho phép tô tự do, không cần kiểm tra vùng
                this.ctx.lineTo(pos.x, pos.y);
                this.ctx.stroke();

                // Bắt đầu path mới từ vị trí hiện tại để tiếp tục
                this.ctx.beginPath();
                this.ctx.moveTo(pos.x, pos.y);
            }

            stopDrawing() {
                if (this.isDrawing) {
                    this.isDrawing = false;
                    this.ctx.beginPath();

                    // Vẽ lại outline với multiply mode như code mẫu
                    this.redrawOutlineWithMultiply();
                    this.saveState();
                }
            }

            redrawOutlineWithMultiply() {
                // Sử dụng multiply mode để outline luôn hiển thị
                this.ctx.globalCompositeOperation = 'multiply';
                this.drawCapybaraOutlineOnly(this.ctx);
                this.ctx.globalCompositeOperation = 'source-over';
            }

            updateBrushPreview(e) {
                const pos = this.getMousePos(e);
                const rect = this.canvas.getBoundingClientRect();
                
                this.brushPreview.style.left = (e.clientX - this.brushSize.value / 2) + 'px';
                this.brushPreview.style.top = (e.clientY - this.brushSize.value / 2) + 'px';
                this.brushPreview.style.width = this.brushSize.value + 'px';
                this.brushPreview.style.height = this.brushSize.value + 'px';
                this.brushPreview.style.backgroundColor = this.colorPicker.value;
            }

            saveState() {
                this.historyStep++;
                if (this.historyStep < this.history.length) {
                    this.history.length = this.historyStep;
                }
                this.history.push(this.canvas.toDataURL());
            }

            undo() {
                if (this.historyStep > 0) {
                    this.historyStep--;
                    this.restoreState(this.history[this.historyStep]);
                }
            }

            restoreState(dataUrl) {
                const img = new Image();
                img.onload = () => {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    this.ctx.drawImage(img, 0, 0);
                };
                img.src = dataUrl;
            }

            clearCanvas() {
                if (confirm('Bạn có chắc muốn xóa hết không?')) {
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                    this.drawCapybaraOutline();
                    this.saveState();
                }
            }

            saveImage() {
                // Tạo canvas tạm để xuất hình không có background trắng
                const tempCanvas = document.createElement('canvas');
                const tempCtx = tempCanvas.getContext('2d');
                tempCanvas.width = this.canvas.width;
                tempCanvas.height = this.canvas.height;

                // Vẽ background trắng
                tempCtx.fillStyle = 'white';
                tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                // Vẽ nội dung canvas hiện tại lên trên
                tempCtx.drawImage(this.canvas, 0, 0);

                // Tạo link download
                const link = document.createElement('a');
                const currentDate = new Date();
                const dateString = currentDate.toISOString().slice(0, 19).replace(/:/g, '-');
                link.download = `capybara-coloring-${dateString}.png`;

                // Chuyển canvas thành blob và tạo URL
                tempCanvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    link.href = url;

                    // Trigger download
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Cleanup
                    URL.revokeObjectURL(url);

                    // Hiển thị thông báo
                    this.showNotification('Đã lưu hình thành công! 🎉');
                }, 'image/png', 1.0);
            }

            showNotification(message) {
                // Tạo notification popup
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #4CAF50;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10000;
                    font-weight: bold;
                    animation: slideIn 0.3s ease;
                `;

                // Thêm CSS animation
                if (!document.querySelector('#notification-style')) {
                    const style = document.createElement('style');
                    style.id = 'notification-style';
                    style.textContent = `
                        @keyframes slideIn {
                            from { transform: translateX(100%); opacity: 0; }
                            to { transform: translateX(0); opacity: 1; }
                        }
                        @keyframes slideOut {
                            from { transform: translateX(0); opacity: 1; }
                            to { transform: translateX(100%); opacity: 0; }
                        }
                    `;
                    document.head.appendChild(style);
                }

                notification.textContent = message;
                document.body.appendChild(notification);

                // Tự động ẩn sau 3 giây
                setTimeout(() => {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // Initialize the app when page loads
        window.addEventListener('load', () => {
            new ColoringApp();
        });
    </script>
</body>
</html>
