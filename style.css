* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #fff;
    background-color: #0a0a0a;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-gif {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    padding: 0 20px;
}

.hero-subtitle {
    font-size: 16px;
    color: #4a90e2;
    margin-bottom: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-title {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 30px;
    line-height: 1.2;
}

.hero-meta {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.duration {
    background: #4a90e2;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
}

.instructor {
    display: flex;
    align-items: center;
    gap: 10px;
}

.instructor-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* Course Section */
.course-section {
    padding: 80px 0;
    background: #1a1a1a;
}

.section-title {
    font-size: 32px;
    text-align: center;
    margin-bottom: 20px;
    color: #fff;
}

.section-subtitle {
    text-align: center;
    margin-bottom: 50px;
    color: #ccc;
    font-size: 18px;
}

.course-tools {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.tool-item {
    text-align: center;
}

.tool-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    border-radius: 10px;
    overflow: hidden;
}

.tool-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cta-button {
    display: block;
    margin: 0 auto;
    background: #4a90e2;
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 18px;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s;
}

.cta-button:hover {
    background: #357abd;
}

/* Products Section */
.products-section {
    padding: 80px 0;
    background: #0a0a0a;
}

.products-swiper {
    margin-top: 50px;
}

.product-card {
    background: #1a1a1a;
    border-radius: 10px;
    overflow: hidden;
    height: 100%;
}

.product-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 20px;
}

.product-info h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #4a90e2;
}

.product-info p {
    color: #ccc;
    line-height: 1.6;
}

/* About Section */
.about-section {
    padding: 80px 0;
    background: #1a1a1a;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    margin-bottom: 15px;
    color: #ccc;
    font-size: 16px;
}

.highlight {
    color: #4a90e2 !important;
    font-weight: bold;
}

.video-container {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.video-thumbnail {
    width: 100%;
    height: 300px;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(74, 144, 226, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: background 0.3s;
}

.play-button:hover {
    background: rgba(74, 144, 226, 1);
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background: #0a0a0a;
}

.testimonials-swiper {
    margin-top: 50px;
}

.testimonial-card {
    background: #1a1a1a;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    height: 100%;
}

.testimonial-card img {
    width: 80px;
    height: 100px;
    object-fit: cover;
    border-radius: 10px;
    margin: 0 auto 20px;
}

.testimonial-content p {
    color: #ccc;
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.6;
}

.testimonial-content h4 {
    color: #4a90e2;
    font-size: 18px;
}

/* Footer */
.footer {
    background: #000;
    padding: 40px 0;
    text-align: center;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.footer-info p {
    margin-bottom: 5px;
    color: #ccc;
    font-size: 14px;
}

/* Swiper Customization */
.swiper-pagination-bullet {
    background: #4a90e2;
}

.swiper-button-next,
.swiper-button-prev {
    color: #4a90e2;
}

/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 32px;
    }
    
    .course-tools {
        gap: 30px;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .hero-meta {
        flex-direction: column;
        gap: 15px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
    }
}
